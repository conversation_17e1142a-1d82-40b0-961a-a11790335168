# ScopeSentry分布式资产扫描系统 - 消息推送与Webhook机制技术文档

## 📋 文档概述

本文档深入分析ScopeSentry分布式资产扫描系统的消息推送机制和webhook触发点，包括Redis发布/订阅模式、WebSocket实时通信、扫描结果通知和系统状态变化等核心功能的实现细节。

**关键特性：**
- 🔄 Redis发布/订阅实时消息传递
- 🌐 WebSocket双向通信机制
- 📢 多类型webhook通知触发
- 📊 任务状态和进度实时更新
- 🔔 扫描结果智能通知

---

## 🏗️ 消息推送架构概览

### 整体架构设计

ScopeSentry采用多层次的消息推送架构，实现了高效的分布式通信：

1. **Redis发布/订阅层**：节点间消息传递
2. **WebSocket通信层**：实时日志推送
3. **Webhook通知层**：外部系统集成
4. **任务状态同步层**：进度和状态更新

### 核心组件交互

```mermaid
graph TB
    subgraph "Web服务器 (Python/FastAPI)"
        WS[WebSocket服务器]
        REDIS_SUB[Redis订阅者]
        WEBHOOK_MGR[Webhook管理器]
        TASK_MGR[任务管理器]
    end
    
    subgraph "Redis消息中间件"
        PUB_SUB[发布/订阅通道]
        TASK_QUEUE[任务队列]
        LOG_CHANNEL[日志通道]
        STATUS_HASH[状态哈希表]
    end
    
    subgraph "扫描节点 (Go语言)"
        SCANNER[扫描引擎]
        REDIS_PUB[Redis发布者]
        NOTIF_QUEUE[通知队列]
        RESULT_HANDLER[结果处理器]
    end
    
    subgraph "外部系统"
        WEBHOOK_ENDPOINT[Webhook端点]
        MONITORING[监控系统]
        ALERT_SYSTEM[告警系统]
    end
    
    %% 消息流向
    SCANNER --> RESULT_HANDLER
    RESULT_HANDLER --> NOTIF_QUEUE
    NOTIF_QUEUE --> WEBHOOK_ENDPOINT
    
    SCANNER --> REDIS_PUB
    REDIS_PUB --> PUB_SUB
    PUB_SUB --> REDIS_SUB
    REDIS_SUB --> WS
    
    TASK_MGR --> TASK_QUEUE
    TASK_QUEUE --> SCANNER
    
    WEBHOOK_MGR --> WEBHOOK_ENDPOINT
    
    %% 样式
    classDef webserver fill:#e1f5fe
    classDef middleware fill:#f3e5f5
    classDef scannode fill:#e8f5e8
    classDef external fill:#fff3e0
    
    class WS,REDIS_SUB,WEBHOOK_MGR,TASK_MGR webserver
    class PUB_SUB,TASK_QUEUE,LOG_CHANNEL,STATUS_HASH middleware
    class SCANNER,REDIS_PUB,NOTIF_QUEUE,RESULT_HANDLER scannode
    class WEBHOOK_ENDPOINT,MONITORING,ALERT_SYSTEM external
```

---

## 🔄 Redis发布/订阅机制

### 发布/订阅实现

#### 订阅端实现 (Webserver)

**位置：** `ScopeSentry/core/redis_handler.py:81-101`

```python
async def subscribe_log_channel():
    channel_name = 'logs'
    logger.info(f"Subscribed to channel {channel_name}")
    while True:
        try:
            async for redis_client in get_redis_pool():
                async with redis_client.pubsub() as pubsub:
                    await pubsub.psubscribe(channel_name)
                    while True:
                        message = await pubsub.get_message(ignore_subscribe_messages=True, timeout=3)
                        if message is not None:
                            data = json.loads(message["data"])
                            log_name = data["name"]
                            if log_name in GET_LOG_NAME:
                                if log_name not in LOG_INFO:
                                    LOG_INFO[log_name] = []
                                LOG_INFO[log_name].append(data['log'])
                            if "Register Success" in data['log']:
                                await check_node_task(log_name, redis_client)
                            await redis_client.rpush(f'log:{log_name}', data['log'])
```

**关键特性：**
- 使用模式订阅 (`psubscribe`) 支持通配符匹配
- 3秒超时机制防止阻塞
- 自动重连机制保证可靠性
- 节点注册检测和任务分发

#### 发布端实现 (Scannode)

**位置：** `ScopeSentry-Scan/pkg/logger/logger.go` (推断)

```go
// 日志发布到Redis
func PublishLog(nodeName, logMessage string) {
    data := map[string]interface{}{
        "name": nodeName,
        "log":  logMessage,
        "time": time.Now().Format("2006-01-02 15:04:05"),
    }
    
    jsonData, _ := json.Marshal(data)
    redis.RedisClient.Publish(context.Background(), "logs", string(jsonData))
}
```

### Redis数据结构设计

| Key模式 | 数据类型 | 用途 | 示例 |
|---------|----------|------|------|
| `logs` | Pub/Sub | 实时日志通道 | `{"name":"node1","log":"扫描开始"}` |
| `NodeTask:{node_name}` | List | 节点任务队列 | 任务JSON数据 |
| `TaskInfo:{task_id}` | List | 任务详细信息 | 目标列表 |
| `TaskInfo:progress:{task_id}:{target}` | Hash | 任务进度信息 | 模块执行状态 |
| `node:{node_name}` | Hash | 节点状态信息 | CPU、内存、任务数 |
| `log:{node_name}` | List | 节点历史日志 | 日志消息列表 |
| `refresh_config:{node_name}` | List | 配置更新队列 | 配置变更通知 |

---

## 🌐 WebSocket实时通信

### WebSocket服务器实现

**位置：** `ScopeSentry/main.py:198-221`

```python
@app.websocket("/")
async def websocket_endpoint(websocket: WebSocket):
    await websocket.accept()
    node_name = ""
    try:
        while True:
            data = await websocket.receive_text()
            try:
                message = json.loads(data)
                node_name = message.get("node_name")
                if node_name:
                    GET_LOG_NAME.append(node_name)
                    # 发送历史日志
                    if node_name in LOG_INFO:
                        while LOG_INFO[node_name]:
                            log = LOG_INFO[node_name].pop(0)
                            await websocket.send_text(log)
                else:
                    await websocket.send_text("Invalid message format: missing node_name")
            except json.JSONDecodeError:
                await websocket.send_text("Invalid JSON format")
    except WebSocketDisconnect:
        if node_name in GET_LOG_NAME:
            GET_LOG_NAME.remove(node_name)
```

### WebSocket消息格式

#### 客户端请求格式
```json
{
    "node_name": "scannode-001",
    "action": "subscribe_logs"
}
```

#### 服务器响应格式
```json
{
    "timestamp": "2025-01-08 10:30:45",
    "node_name": "scannode-001",
    "level": "INFO",
    "message": "SubdomainScan module completed for target: example.com",
    "module": "SubdomainScan",
    "target": "example.com"
}
```

### WebSocket连接管理

**全局变量：**
```python
# 存储WebSocket连接的节点名称
GET_LOG_NAME = []

# 存储节点日志信息
LOG_INFO = {}
```

**问题分析：**
- ❌ 缺乏身份认证机制
- ❌ 无连接数限制
- ❌ 内存泄漏风险（LOG_INFO持续增长）
- ❌ 无心跳检测机制

---

## 📢 Webhook通知系统

### Webhook配置管理

#### 配置数据结构

**位置：** `ScopeSentry/api/notification.py:24`

```python
# Webhook配置字段
{
    "name": "钉钉群通知",           # 通知名称
    "method": "POST",             # HTTP方法 (GET/POST)
    "url": "https://oapi.dingtalk.com/robot/send?access_token=xxx",
    "contentType": "application/json",  # 内容类型
    "data": "{\"msgtype\":\"text\",\"text\":{\"content\":\"*msg*\"}}",  # 请求体模板
    "state": true                 # 启用状态
}
```

#### 通知开关配置

**位置：** `ScopeSentry/core/db.py:88-96`

```python
# 通知配置初始化
notification_config = {
    "name": "notification",
    "dirScanNotification": True,        # 目录扫描通知
    "portScanNotification": True,       # 端口扫描通知
    "sensitiveNotification": True,      # 敏感信息通知
    "subdomainTakeoverNotification": True,  # 子域名接管通知
    "pageMonNotification": True,        # 页面监控通知
    "subdomainNotification": True,      # 子域名发现通知
    "vulNotification": True,            # 漏洞发现通知
    "type": "notification"
}
```

### Webhook触发点分析

#### 1. 子域名发现通知

**触发位置：** `ScopeSentry-Scan/internal/results/handler.go:69-72`

```go
func (h *handler) Subdomain(result *types.SubdomainResult) {
    // ... 数据处理 ...
    if global.NotificationConfig.SubdomainScan {
        NotificationMsg := fmt.Sprintf("%v - %v\n", result.Host, result.IP)
        notification.NotificationQueues["SubdomainScan"].Queue <- NotificationMsg
    }
    ResultQueues["SubdomainScan"].Queue <- interfaceSlice
}
```

**消息格式：**
```
[scannode-001][SubdomainScan]results:
api.example.com - ***********00
admin.example.com - ***********01
```

#### 2. 漏洞发现通知

**触发位置：** `ScopeSentry-Scan/internal/results/handler.go:301-320`

```go
func (h *handler) Vulnerability(result *types.VulnerabilityResult) {
    // ... 数据处理 ...
    if global.NotificationConfig.VulNotification {
        NotificationMsg := ""
        if global.NotificationConfig.VulLevel != "" {
            // 根据漏洞等级过滤
            if strings.Contains(strings.ToLower(global.NotificationConfig.VulLevel), 
                               strings.ToLower(result.Level)+",") {
                if result.Url != "" {
                    NotificationMsg += fmt.Sprintf("%v-[%v]-[%v]-[%v]\n", 
                        result.Url, result.Level, result.VulName, result.Matched)
                }
            }
        }
        notification.NotificationQueues["VulnerabilityScan"].Queue <- NotificationMsg
    }
}
```

**消息格式：**
```
[scannode-001][VulnerabilityScan]results:
https://example.com/admin-[HIGH]-[SQL Injection]-[union select]
https://example.com/login-[MEDIUM]-[XSS Reflected]-[<script>alert(1)</script>]
```

#### 3. 敏感信息泄露通知

**触发位置：** `ScopeSentry-Scan/internal/results/handler.go:223-226`

```go
func (h *handler) SensitiveResult(result *types.SensitiveResult) {
    // ... 数据处理 ...
    if global.NotificationConfig.SensitiveNotification {
        NotificationMsg := fmt.Sprintf("Sensitive Scan:\n%v - %v\n", result.Url, result.SID)
        notification.NotificationQueues["URLSecurity"].Queue <- NotificationMsg
    }
}
```

#### 4. 子域名接管通知

**触发位置：** `ScopeSentry-Scan/internal/results/handler.go:85-88`

```go
func (h *handler) SubdomainTakeover(result *types.SubTakeResult) {
    // ... 数据处理 ...
    if global.NotificationConfig.SubdomainTakeoverNotification {
        NotificationMsg := fmt.Sprintf("Subdomain Takeover:\n%v - %v\n", result.Input, result.Cname)
        notification.NotificationQueues["SubdomainSecurity"].Queue <- NotificationMsg
    }
}
```

#### 5. 目录扫描通知

**触发位置：** `ScopeSentry-Scan/internal/results/handler.go:277-285`

```go
func (h *handler) Dir(result *types.DirResult) {
    // ... 数据处理 ...
    if global.NotificationConfig.DirScanNotification {
        NotificationMsg := ""
        if result.Msg != "" {
            NotificationMsg = fmt.Sprintf("%v - %v -%v\n", result.Url, result.Status, result.Msg)
        } else {
            NotificationMsg = fmt.Sprintf("%v - %v\n", result.Url, result.Status)
        }
        notification.NotificationQueues["DirScan"].Queue <- NotificationMsg
    }
}
```

#### 6. 新资产发现通知

**触发位置：** `ScopeSentry-Scan/internal/results/handler.go:366-369`

```go
// 新根域名发现
if global.NotificationConfig.NewAsset {
    tmp := fmt.Sprintf("Found a new root domain name: %v - %v - %v - %v", 
        result.Domain, result.ICP, result.Company, result.Project)
    notification.NotificationQueues["NewAssets"].Queue <- tmp
}
```

### Webhook发送机制

#### 批量处理机制

**位置：** `ScopeSentry-Scan/internal/notification/notification.go:46-78`

```go
const (
    batchSize     = 20                    // 批量大小
    flushInterval = 2 * time.Second       // 刷新间隔
)

func processQueue(module string, mq *NotificationQueue) {
    ticker := time.NewTicker(flushInterval)
    defer ticker.Stop()

    for {
        select {
        case <-ticker.C:
            processBatch(module, mq)
        }
    }
}

func processBatch(module string, mq *NotificationQueue) {
    var buffer = ""
    num := 0
    // 批量获取消息
    for i := 0; i < batchSize; i++ {
        select {
        case msg := <-mq.Queue:
            num += 1
            buffer += msg
        default:
            break
        }
    }

    if num > 0 {
        FlushBuffer(module, &buffer)
    }
}
```

#### HTTP请求发送

**位置：** `ScopeSentry-Scan/internal/notification/notification.go:81-101`

```go
func FlushBuffer(module string, buffer *string) {
    // 格式化消息
    *buffer = "[" + global.AppConfig.NodeName + "][" + module + "]results:\n" + *buffer
    
    // 遍历所有配置的webhook
    for _, api := range global.NotificationApi {
        uri := strings.Replace(api.Url, "*msg*", *buffer, -1)
        
        if api.Method == "GET" {
            _, err := utils.Requests.HttpGet(uri)
            if err != nil {
                logger.SlogError(fmt.Sprintf("SendNotification %v HTTP Get Error: %v", uri, err))
            }
        } else {
            data := strings.Replace(api.Data, "*msg*", *buffer, -1)
            err, _ := utils.Requests.HttpPost(uri, []byte(data), api.ContentType)
            if err != nil {
                logger.SlogError(fmt.Sprintf("SendNotification %v HTTP Post Error: %v", uri, err))
            }
        }
    }
    
    // 清空缓冲区
    *buffer = ""
}
```

---

## 📊 任务状态同步机制

### 任务进度追踪

#### 进度开始记录

**位置：** `ScopeSentry-Scan/internal/handler/task.go:60-78`

```go
func (h *Handle) ProgressStart(typ string, target string, taskId string, flag int) {
    if flag == 0 {
        return
    }
    logger.SlogInfo(fmt.Sprintf("%v module start scanning the target: %v", typ, target))
    
    key := "TaskInfo:progress:" + taskId + ":" + target
    ty := typ + "_start"
    ProgressInfo := map[string]interface{}{
        ty: utils.Tools.GetTimeNow(),
    }
    
    if typ == "scan" {
        ProgressInfo["node"] = global.AppConfig.NodeName
    }
    
    err := redis.RedisClient.HMSet(context.Background(), key, ProgressInfo)
    if err != nil {
        logger.SlogError(fmt.Sprintf("ProgressStart redis error: %s", err))
    }
}
```

#### 进度结束记录

**位置：** `ScopeSentry-Scan/internal/handler/task.go:80-95`

```go
func (h *Handle) ProgressEnd(typ string, target string, taskId string, flag int) {
    if flag == 0 {
        return
    }
    logger.SlogInfo(fmt.Sprintf("%v module end scanning the target: %v", typ, target))
    
    key := "TaskInfo:progress:" + taskId + ":" + target
    ty := typ + "_end"
    ProgressInfo := map[string]interface{}{
        ty: utils.Tools.GetTimeNow(),
    }
    
    err := redis.RedisClient.HMSet(context.Background(), key, ProgressInfo)
    if err != nil {
        logger.SlogError(fmt.Sprintf("ProgressEnd redis error: %s", err))
    }
}
```

### 任务完成通知

**位置：** `ScopeSentry-Scan/internal/handler/task.go:97-111`

```go
func (h *Handle) TaskEnd(target string, taskId string) {
    // 记录任务结束时间
    key := "TaskInfo:time:" + taskId
    err := redis.RedisClient.Set(context.Background(), key, utils.Tools.GetTimeNow())
    if err != nil {
        logger.SlogError(fmt.Sprintf("TaskEnds push redis error: %s", err))
        return
    }

    // 记录完成的目标
    key = "TaskInfo:tmp:" + taskId
    _, err = redis.RedisClient.SAdd(context.Background(), key, target)
    if err != nil {
        logger.SlogError(fmt.Sprintf("TaskEnds push redis error: %s", err))
    }
}
```

### 节点状态同步

#### 节点注册和心跳

**位置：** `ScopeSentry-Scan/internal/node/node.go:23-75`

```go
func Register() {
    nodeName := global.AppConfig.NodeName
    key := "node:" + nodeName
    firstRegister := true
    ticker := time.Tick(20 * time.Second)  // 20秒心跳间隔
    
    for {
        if firstRegister {
            // 首次注册
            memInfo, _ := mem.VirtualMemory()
            nodeInfo := map[string]interface{}{
                "updateTime":    utils.Tools.GetTimeNow(),
                "running":       0,
                "finished":      0,
                "cpuNum":        0,
                "TotleMem":      float64(memInfo.Total) / 1024 / 1024,
                "memNum":        0,
                "state":         1, // 1运行中 2暂停 3未连接
                "version":       global.VERSION,
                "modulesConfig": modulesConfig,
            }
            
            err = redis.RedisClient.HMSet(context.Background(), key, nodeInfo)
            if err != nil {
                logger.SlogErrorLocal(fmt.Sprintf("Error setting initial values: %s", err))
                return
            }
            
            logger.SlogInfo(fmt.Sprintf("Register Success:%v - version %v", nodeName, global.VERSION))
            firstRegister = false
        } else {
            // 定期更新状态
            cpuNum, memNum := utils.Tools.GetSystemUsage()
            run, fin := handler.TaskHandle.GetRunFin()
            
            nodeInfo := map[string]interface{}{
                "updateTime": utils.Tools.GetTimeNow(),
                "cpuNum":     cpuNum,
                "memNum":     memNum,
                "maxTaskNum": config.ModulesConfig.MaxGoroutineCount,
                "running":    run,
                "finished":   fin,
                "state":      global.AppConfig.State,
                "version":    global.VERSION,
            }
            
            err := redis.RedisClient.HMSet(context.Background(), key, nodeInfo)
            if err != nil {
                logger.SlogErrorLocal(fmt.Sprintf("Error setting initial values: %s", err))
                continue
            }
        }
        <-ticker
    }
}
```

---

## 🔧 配置管理和同步

### 配置更新机制

**位置：** `ScopeSentry/core/redis_handler.py:61-78`

```python
async def refresh_config(name, t, content=None):
    data = {
        "name": name,
        "type": t,
    }
    if content is not None:
        data['content'] = content
        
    async for redis_client in get_redis_pool():
        name_all = []
        if name == "all":
            # 获取所有节点
            keys = await redis_client.keys("node:*")
            for key in keys:
                tmp_name = key.split(":")[1]
                name_all.append(tmp_name)
        else:
            name_all.append(name)
            
        # 向指定节点推送配置更新
        for n in name_all:
            await redis_client.rpush(f"refresh_config:{n}", json.dumps(data))
```

### 通知配置API

#### 获取通知配置

**位置：** `ScopeSentry/api/notification.py:113-127`

```python
@router.get("/notification/config/data")
async def get_notification_config_data(db=Depends(get_mongo_db), _: dict = Depends(verify_token)):
    try:
        result = await db.config.find_one({"name": "notification"})
        del result['_id']
        del result['type']
        del result['name']
        return {
            "code": 200,
            "data": result
        }
    except Exception as e:
        logger.error(str(e))
        return {"message": "error", "code": 500}
```

#### 更新通知配置

**位置：** `ScopeSentry/api/notification.py:130-147`

```python
@router.post("/notification/config/update")
async def update_notification_config_data(request_data: dict, db=Depends(get_mongo_db), _: dict = Depends(verify_token)):
    try:
        update_document = {
            "$set": request_data
        }

        result = await db.config.update_one({"name": "notification"}, update_document)
        if result:
            # 通知所有节点配置更新
            await refresh_config('all', 'notification')
            return {"message": "Data updated successfully", "code": 200}
        else:
            return {"message": "Failed to update data", "code": 404}
    except Exception as e:
        logger.error(str(e))
        return {"message": "error", "code": 500}
```

---

## 📈 性能分析和优化建议

### 当前实现的优缺点

#### ✅ 优势
1. **异步处理**：使用异步I/O提高并发性能
2. **批量处理**：webhook通知采用批量发送机制
3. **解耦设计**：消息队列实现组件解耦
4. **实时性**：WebSocket提供实时日志推送

#### ❌ 问题
1. **内存泄漏**：LOG_INFO字典持续增长
2. **无认证机制**：WebSocket连接缺乏安全验证
3. **错误处理不足**：webhook发送失败无重试机制
4. **资源限制缺失**：无连接数和消息大小限制

### 优化建议

#### 1. WebSocket连接优化

```python
import asyncio
from typing import Dict, Set
from fastapi import WebSocket
import jwt

class WebSocketManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.node_subscribers: Dict[str, Set[str]] = {}
        self.max_connections = 100
        self.max_log_buffer = 1000
    
    async def connect(self, websocket: WebSocket, token: str):
        # JWT认证
        try:
            payload = jwt.decode(token, SECRET_KEY, algorithms=["HS256"])
            user_id = payload.get("sub")
        except jwt.InvalidTokenError:
            await websocket.close(code=4001, reason="Invalid token")
            return False
        
        # 连接数限制
        if len(self.active_connections) >= self.max_connections:
            await websocket.close(code=4002, reason="Too many connections")
            return False
        
        await websocket.accept()
        connection_id = f"{user_id}_{int(time.time())}"
        self.active_connections[connection_id] = websocket
        return connection_id
    
    async def disconnect(self, connection_id: str):
        if connection_id in self.active_connections:
            del self.active_connections[connection_id]
        
        # 清理订阅关系
        for node_name in self.node_subscribers:
            self.node_subscribers[node_name].discard(connection_id)
    
    async def subscribe_node_logs(self, connection_id: str, node_name: str):
        if node_name not in self.node_subscribers:
            self.node_subscribers[node_name] = set()
        self.node_subscribers[node_name].add(connection_id)
    
    async def broadcast_log(self, node_name: str, log_message: str):
        if node_name in self.node_subscribers:
            disconnected = []
            for connection_id in self.node_subscribers[node_name]:
                if connection_id in self.active_connections:
                    try:
                        await self.active_connections[connection_id].send_text(log_message)
                    except:
                        disconnected.append(connection_id)
                else:
                    disconnected.append(connection_id)
            
            # 清理断开的连接
            for conn_id in disconnected:
                self.node_subscribers[node_name].discard(conn_id)

# 全局WebSocket管理器
ws_manager = WebSocketManager()
```

#### 2. Webhook重试机制

```go
package notification

import (
    "context"
    "time"
    "math"
)

type WebhookRetryConfig struct {
    MaxRetries    int           `json:"maxRetries"`
    InitialDelay  time.Duration `json:"initialDelay"`
    MaxDelay      time.Duration `json:"maxDelay"`
    BackoffFactor float64       `json:"backoffFactor"`
}

type WebhookMessage struct {
    URL         string            `json:"url"`
    Method      string            `json:"method"`
    Data        string            `json:"data"`
    ContentType string            `json:"contentType"`
    RetryCount  int               `json:"retryCount"`
    NextRetry   time.Time         `json:"nextRetry"`
}

func SendWebhookWithRetry(ctx context.Context, msg *WebhookMessage, config WebhookRetryConfig) error {
    var err error
    
    for attempt := 0; attempt <= config.MaxRetries; attempt++ {
        if attempt > 0 {
            // 指数退避
            delay := time.Duration(float64(config.InitialDelay) * math.Pow(config.BackoffFactor, float64(attempt-1)))
            if delay > config.MaxDelay {
                delay = config.MaxDelay
            }
            
            select {
            case <-ctx.Done():
                return ctx.Err()
            case <-time.After(delay):
            }
        }
        
        // 发送webhook
        if msg.Method == "GET" {
            _, err = utils.Requests.HttpGet(msg.URL)
        } else {
            err, _ = utils.Requests.HttpPost(msg.URL, []byte(msg.Data), msg.ContentType)
        }
        
        if err == nil {
            return nil // 成功发送
        }
        
        logger.SlogWarn(fmt.Sprintf("Webhook attempt %d failed: %v", attempt+1, err))
    }
    
    // 所有重试都失败，记录到死信队列
    logger.SlogError(fmt.Sprintf("Webhook failed after %d attempts: %v", config.MaxRetries+1, err))
    return err
}
```

#### 3. 消息队列优化

```go
package notification

import (
    "context"
    "sync"
    "time"
)

type PriorityQueue struct {
    high   chan string
    normal chan string
    low    chan string
    ctx    context.Context
    cancel context.CancelFunc
    wg     sync.WaitGroup
}

func NewPriorityQueue(highCap, normalCap, lowCap int) *PriorityQueue {
    ctx, cancel := context.WithCancel(context.Background())
    return &PriorityQueue{
        high:   make(chan string, highCap),
        normal: make(chan string, normalCap),
        low:    make(chan string, lowCap),
        ctx:    ctx,
        cancel: cancel,
    }
}

func (pq *PriorityQueue) Start() {
    pq.wg.Add(1)
    go pq.processMessages()
}

func (pq *PriorityQueue) Stop() {
    pq.cancel()
    pq.wg.Wait()
}

func (pq *PriorityQueue) processMessages() {
    defer pq.wg.Done()
    ticker := time.NewTicker(1 * time.Second)
    defer ticker.Stop()
    
    for {
        select {
        case <-pq.ctx.Done():
            return
        case <-ticker.C:
            pq.processBatch()
        }
    }
}

func (pq *PriorityQueue) processBatch() {
    var messages []string
    
    // 优先处理高优先级消息
    for i := 0; i < 10; i++ {
        select {
        case msg := <-pq.high:
            messages = append(messages, msg)
        default:
            goto normal
        }
    }
    
normal:
    // 处理普通优先级消息
    for i := 0; i < 15; i++ {
        select {
        case msg := <-pq.normal:
            messages = append(messages, msg)
        default:
            goto low
        }
    }
    
low:
    // 处理低优先级消息
    for i := 0; i < 5; i++ {
        select {
        case msg := <-pq.low:
            messages = append(messages, msg)
        default:
            goto send
        }
    }
    
send:
    if len(messages) > 0 {
        // 批量发送消息
        batchMessage := strings.Join(messages, "\n")
        sendWebhookBatch(batchMessage)
    }
}

func (pq *PriorityQueue) SendHigh(message string) {
    select {
    case pq.high <- message:
    default:
        // 高优先级队列满，记录警告
        logger.SlogWarn("High priority queue is full, message dropped")
    }
}

func (pq *PriorityQueue) SendNormal(message string) {
    select {
    case pq.normal <- message:
    default:
        // 降级到低优先级
        pq.SendLow(message)
    }
}

func (pq *PriorityQueue) SendLow(message string) {
    select {
    case pq.low <- message:
    default:
        // 低优先级队列满，丢弃消息
        logger.SlogWarn("All queues are full, message dropped")
    }
}
```

---

## 🔒 安全性考虑

### 当前安全问题

1. **WebSocket无认证**：任何人都可以连接并获取日志
2. **消息内容未加密**：敏感信息可能泄露
3. **无访问控制**：缺乏基于角色的权限管理
4. **Webhook URL验证不足**：可能被恶意利用

### 安全改进建议

#### 1. WebSocket认证

```python
from fastapi import WebSocket, HTTPException
import jwt

async def authenticate_websocket(websocket: WebSocket, token: str):
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=["HS256"])
        user_id = payload.get("sub")
        permissions = payload.get("permissions", [])
        
        # 检查WebSocket权限
        if "websocket:logs" not in permissions:
            await websocket.close(code=4003, reason="Insufficient permissions")
            return None
            
        return {"user_id": user_id, "permissions": permissions}
    except jwt.InvalidTokenError:
        await websocket.close(code=4001, reason="Invalid token")
        return None
```

#### 2. 消息加密

```python
from cryptography.fernet import Fernet
import base64

class MessageEncryption:
    def __init__(self, key: bytes):
        self.cipher = Fernet(key)
    
    def encrypt_message(self, message: str) -> str:
        encrypted = self.cipher.encrypt(message.encode())
        return base64.b64encode(encrypted).decode()
    
    def decrypt_message(self, encrypted_message: str) -> str:
        encrypted_bytes = base64.b64decode(encrypted_message.encode())
        decrypted = self.cipher.decrypt(encrypted_bytes)
        return decrypted.decode()

# 使用示例
encryption = MessageEncryption(ENCRYPTION_KEY)
encrypted_log = encryption.encrypt_message(log_message)
```

#### 3. Webhook URL验证

```python
import re
from urllib.parse import urlparse

class WebhookValidator:
    ALLOWED_SCHEMES = ['https']  # 只允许HTTPS
    BLOCKED_HOSTS = ['localhost', '127.0.0.1', '0.0.0.0']  # 阻止内网地址
    
    @classmethod
    def validate_webhook_url(cls, url: str) -> bool:
        try:
            parsed = urlparse(url)
            
            # 检查协议
            if parsed.scheme not in cls.ALLOWED_SCHEMES:
                return False
            
            # 检查主机名
            if parsed.hostname in cls.BLOCKED_HOSTS:
                return False
            
            # 检查是否为内网地址
            if cls.is_private_ip(parsed.hostname):
                return False
            
            return True
        except Exception:
            return False
    
    @staticmethod
    def is_private_ip(hostname: str) -> bool:
        import ipaddress
        try:
            ip = ipaddress.ip_address(hostname)
            return ip.is_private
        except ValueError:
            return False
```

---

## 📚 API文档和使用示例

### Webhook配置API

#### 添加Webhook配置

```http
POST /api/notification/add
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
    "name": "钉钉群通知",
    "method": "POST",
    "url": "https://oapi.dingtalk.com/robot/send?access_token=xxx",
    "contentType": "application/json",
    "data": "{\"msgtype\":\"text\",\"text\":{\"content\":\"*msg*\"}}",
    "state": true
}
```

#### 更新通知开关

```http
POST /api/notification/config/update
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
    "subdomainNotification": true,
    "vulNotification": true,
    "vulLevel": "HIGH,CRITICAL,",
    "sensitiveNotification": false,
    "dirScanNotification": true,
    "newAsset": true
}
```

### WebSocket连接示例

#### JavaScript客户端

```javascript
class ScopeSentryWebSocket {
    constructor(token, nodeNames = []) {
        this.token = token;
        this.nodeNames = nodeNames;
        this.ws = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
    }
    
    connect() {
        const wsUrl = `ws://localhost:8082/?token=${this.token}`;
        this.ws = new WebSocket(wsUrl);
        
        this.ws.onopen = () => {
            console.log('WebSocket连接已建立');
            this.reconnectAttempts = 0;
            
            // 订阅节点日志
            this.nodeNames.forEach(nodeName => {
                this.subscribeNodeLogs(nodeName);
            });
        };
        
        this.ws.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.handleLogMessage(data);
            } catch (e) {
                // 处理纯文本日志
                this.handleTextLog(event.data);
            }
        };
        
        this.ws.onclose = (event) => {
            console.log('WebSocket连接已关闭', event.code, event.reason);
            this.handleReconnect();
        };
        
        this.ws.onerror = (error) => {
            console.error('WebSocket错误:', error);
        };
    }
    
    subscribeNodeLogs(nodeName) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            const message = {
                action: 'subscribe',
                node_name: nodeName
            };
            this.ws.send(JSON.stringify(message));
        }
    }
    
    handleLogMessage(data) {
        // 处理结构化日志消息
        console.log(`[${data.timestamp}] ${data.node_name}: ${data.message}`);
        
        // 更新UI
        this.updateLogDisplay(data);
    }
    
    handleTextLog(text) {
        // 处理纯文本日志
        console.log('日志:', text);
        this.updateTextDisplay(text);
    }
    
    handleReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            const delay = Math.pow(2, this.reconnectAttempts) * 1000; // 指数退避
            
            console.log(`${delay/1000}秒后尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
            
            setTimeout(() => {
                this.connect();
            }, delay);
        } else {
            console.error('WebSocket重连失败，已达到最大重试次数');
        }
    }
    
    updateLogDisplay(data) {
        // 实现日志显示逻辑
        const logContainer = document.getElementById('log-container');
        const logEntry = document.createElement('div');
        logEntry.className = `log-entry log-${data.level.toLowerCase()}`;
        logEntry.innerHTML = `
            <span class="timestamp">${data.timestamp}</span>
            <span class="node">${data.node_name}</span>
            <span class="module">${data.module}</span>
            <span class="message">${data.message}</span>
        `;
        logContainer.appendChild(logEntry);
        
        // 自动滚动到底部
        logContainer.scrollTop = logContainer.scrollHeight;
    }
    
    disconnect() {
        if (this.ws) {
            this.ws.close();
        }
    }
}

// 使用示例
const wsClient = new ScopeSentryWebSocket('your-jwt-token', ['scannode-001', 'scannode-002']);
wsClient.connect();
```

### Webhook接收端示例

#### Python Flask接收端

```python
from flask import Flask, request, jsonify
import json
import logging

app = Flask(__name__)
logging.basicConfig(level=logging.INFO)

@app.route('/webhook/scopesentry', methods=['POST'])
def receive_scopesentry_webhook():
    try:
        # 验证Content-Type
        if request.content_type != 'application/json':
            return jsonify({'error': 'Invalid content type'}), 400
        
        # 解析消息
        data = request.get_json()
        message = data.get('text', {}).get('content', '')
        
        # 解析ScopeSentry消息格式
        if message.startswith('[') and ']' in message:
            # 提取节点名和模块名
            parts = message.split(']', 2)
            if len(parts) >= 3:
                node_name = parts[0][1:]  # 去掉开头的 [
                module_name = parts[1][1:]  # 去掉开头的 [
                content = parts[2].replace('results:\n', '')
                
                # 处理不同类型的通知
                handle_notification(node_name, module_name, content)
        
        return jsonify({'status': 'success'}), 200
        
    except Exception as e:
        logging.error(f'处理webhook失败: {e}')
        return jsonify({'error': 'Internal server error'}), 500

def handle_notification(node_name, module_name, content):
    """处理不同类型的通知"""
    
    if module_name == 'VulnerabilityScan':
        handle_vulnerability_notification(node_name, content)
    elif module_name == 'SubdomainScan':
        handle_subdomain_notification(node_name, content)
    elif module_name == 'DirScan':
        handle_directory_notification(node_name, content)
    elif module_name == 'NewAssets':
        handle_new_asset_notification(node_name, content)
    else:
        logging.info(f'收到通知 - 节点: {node_name}, 模块: {module_name}, 内容: {content}')

def handle_vulnerability_notification(node_name, content):
    """处理漏洞发现通知"""
    lines = content.strip().split('\n')
    for line in lines:
        if '-[' in line:  # 漏洞格式: URL-[LEVEL]-[NAME]-[MATCHED]
            parts = line.split('-[')
            if len(parts) >= 4:
                url = parts[0]
                level = parts[1].rstrip(']')
                vuln_name = parts[2].rstrip(']')
                matched = parts[3].rstrip(']')
                
                # 发送到安全团队
                send_security_alert({
                    'type': 'vulnerability',
                    'url': url,
                    'level': level,
                    'vulnerability': vuln_name,
                    'evidence': matched,
                    'node': node_name
                })

def handle_subdomain_notification(node_name, content):
    """处理子域名发现通知"""
    lines = content.strip().split('\n')
    subdomains = []
    
    for line in lines:
        if ' - ' in line:  # 格式: subdomain.example.com - ***********
            parts = line.split(' - ')
            if len(parts) == 2:
                subdomain = parts[0].strip()
                ip = parts[1].strip()
                subdomains.append({'subdomain': subdomain, 'ip': ip})
    
    if subdomains:
        # 更新资产清单
        update_asset_inventory(subdomains, node_name)

def send_security_alert(alert_data):
    """发送安全告警"""
    # 实现发送到安全平台的逻辑
    logging.warning(f'安全告警: {alert_data}')

def update_asset_inventory(subdomains, node_name):
    """更新资产清单"""
    # 实现资产清单更新逻辑
    logging.info(f'发现 {len(subdomains)} 个新子域名，来源节点: {node_name}')

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
```

---

## 🎯 最佳实践建议

### 1. 消息设计原则

- **结构化消息**：使用JSON格式传递结构化数据
- **消息去重**：实现消息ID机制避免重复处理
- **优雅降级**：网络异常时的降级策略
- **消息压缩**：大消息使用压缩减少传输开销

### 2. 性能优化策略

- **连接池管理**：复用Redis和HTTP连接
- **批量处理**：合并小消息减少网络开销
- **异步处理**：使用异步I/O提高并发能力
- **内存管理**：定期清理过期数据防止内存泄漏

### 3. 可靠性保障

- **消息持久化**：重要消息持久化到磁盘
- **重试机制**：指数退避重试策略
- **死信队列**：处理失败消息的兜底机制
- **监控告警**：消息队列积压和失败率监控

### 4. 安全防护措施

- **访问控制**：基于JWT的细粒度权限控制
- **消息加密**：敏感消息端到端加密
- **速率限制**：防止消息洪水攻击
- **审计日志**：记录所有消息操作日志

---

---

## 🛠️ 部署配置指南

### Redis配置优化

#### 生产环境Redis配置

```bash
# redis.conf 关键配置项
# 内存优化
maxmemory 2gb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000

# 网络配置
tcp-keepalive 300
timeout 0

# 发布订阅配置
client-output-buffer-limit pubsub 32mb 8mb 60

# 日志配置
loglevel notice
logfile /var/log/redis/redis-server.log

# 安全配置
requirepass your_strong_password
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command CONFIG "CONFIG_9a8b7c6d"
```

#### Redis集群配置

```yaml
# docker-compose.yml Redis集群配置
version: '3.8'
services:
  redis-master:
    image: redis:7-alpine
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis-master-data:/data
    ports:
      - "6379:6379"
    environment:
      - REDIS_REPLICATION_MODE=master

  redis-slave-1:
    image: redis:7-alpine
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD} --slaveof redis-master 6379 --masterauth ${REDIS_PASSWORD}
    volumes:
      - redis-slave1-data:/data
    depends_on:
      - redis-master

  redis-slave-2:
    image: redis:7-alpine
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD} --slaveof redis-master 6379 --masterauth ${REDIS_PASSWORD}
    volumes:
      - redis-slave2-data:/data
    depends_on:
      - redis-master

  redis-sentinel-1:
    image: redis:7-alpine
    command: redis-sentinel /etc/redis/sentinel.conf
    volumes:
      - ./sentinel.conf:/etc/redis/sentinel.conf
    depends_on:
      - redis-master

volumes:
  redis-master-data:
  redis-slave1-data:
  redis-slave2-data:
```

#### Sentinel配置文件

```bash
# sentinel.conf
port 26379
sentinel monitor mymaster redis-master 6379 2
sentinel auth-pass mymaster your_strong_password
sentinel down-after-milliseconds mymaster 5000
sentinel parallel-syncs mymaster 1
sentinel failover-timeout mymaster 10000
```

### 环境变量配置

#### Webserver环境变量

```bash
# .env 文件
# 数据库配置
MONGODB_HOST=mongodb
MONGODB_PORT=27017
MONGODB_USER=scopesentry
MONGODB_PASSWORD=your_mongodb_password
MONGODB_DATABASE=scopesentry

# Redis配置
REDIS_HOST=redis-master
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# JWT配置
JWT_SECRET=your_jwt_secret_key_here
JWT_ALGORITHM=HS256
JWT_EXPIRE_HOURS=24

# WebSocket配置
WEBSOCKET_MAX_CONNECTIONS=100
WEBSOCKET_MAX_MESSAGE_SIZE=1048576
WEBSOCKET_PING_INTERVAL=20
WEBSOCKET_PING_TIMEOUT=10

# 通知配置
NOTIFICATION_BATCH_SIZE=20
NOTIFICATION_FLUSH_INTERVAL=2
NOTIFICATION_RETRY_MAX=3
NOTIFICATION_RETRY_DELAY=1

# 日志配置
LOG_LEVEL=INFO
LOG_MAX_SIZE=100MB
LOG_RETENTION_DAYS=30
```

#### Scannode环境变量

```bash
# scannode.env
# 节点配置
NODE_NAME=scannode-001
NODE_VERSION=1.7.0
MAX_GOROUTINE_COUNT=100

# Redis配置
REDIS_HOST=redis-master
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# MongoDB配置
MONGODB_HOST=mongodb
MONGODB_PORT=27017
MONGODB_USER=scopesentry
MONGODB_PASSWORD=your_mongodb_password
MONGODB_DATABASE=scopesentry

# 通知配置
NOTIFICATION_ENABLED=true
NOTIFICATION_BATCH_SIZE=20
NOTIFICATION_FLUSH_INTERVAL=2s

# 性能配置
WORKER_POOL_SIZE=50
RESULT_BUFFER_SIZE=1000
TASK_TIMEOUT=3600s
```

### Nginx反向代理配置

```nginx
# /etc/nginx/sites-available/scopesentry
upstream scopesentry_backend {
    server 127.0.0.1:8082;
    keepalive 32;
}

# WebSocket upstream
upstream scopesentry_websocket {
    server 127.0.0.1:8082;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    # SSL配置
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    # 安全头部
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

    # 限制请求大小
    client_max_body_size 10M;

    # API请求
    location /api/ {
        proxy_pass http://scopesentry_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # 限制请求频率
        limit_req zone=api burst=20 nodelay;
    }

    # WebSocket连接
    location / {
        proxy_pass http://scopesentry_websocket;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # WebSocket特殊配置
        proxy_read_timeout 86400s;
        proxy_send_timeout 86400s;
        proxy_buffering off;
    }
}

# 限制请求频率
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
```

---

## 🔍 监控和故障排除

### 监控指标

#### Redis监控指标

```python
import redis
import time
from prometheus_client import Gauge, Counter

# Redis连接监控
redis_connections = Gauge('redis_connections_total', 'Total Redis connections')
redis_memory_usage = Gauge('redis_memory_usage_bytes', 'Redis memory usage in bytes')
redis_pubsub_channels = Gauge('redis_pubsub_channels', 'Number of pub/sub channels')
redis_pubsub_patterns = Gauge('redis_pubsub_patterns', 'Number of pub/sub patterns')

# 消息队列监控
message_queue_size = Gauge('message_queue_size', 'Message queue size', ['queue_name'])
message_processed_total = Counter('message_processed_total', 'Total processed messages', ['queue_name', 'status'])

def collect_redis_metrics():
    """收集Redis监控指标"""
    try:
        r = redis.Redis(host=REDIS_HOST, port=REDIS_PORT, password=REDIS_PASSWORD)
        info = r.info()

        # 连接数
        redis_connections.set(info['connected_clients'])

        # 内存使用
        redis_memory_usage.set(info['used_memory'])

        # 发布订阅
        redis_pubsub_channels.set(info['pubsub_channels'])
        redis_pubsub_patterns.set(info['pubsub_patterns'])

        # 队列长度
        for queue_name in ['NodeTask', 'TaskInfo', 'logs']:
            keys = r.keys(f'{queue_name}:*')
            total_size = 0
            for key in keys:
                total_size += r.llen(key)
            message_queue_size.labels(queue_name=queue_name).set(total_size)

    except Exception as e:
        logger.error(f"Failed to collect Redis metrics: {e}")

# 定期收集指标
import threading
def start_metrics_collection():
    def collect_loop():
        while True:
            collect_redis_metrics()
            time.sleep(30)  # 30秒收集一次

    thread = threading.Thread(target=collect_loop, daemon=True)
    thread.start()
```

#### WebSocket监控指标

```python
from prometheus_client import Gauge, Counter, Histogram

# WebSocket连接监控
websocket_connections = Gauge('websocket_connections_active', 'Active WebSocket connections')
websocket_messages_sent = Counter('websocket_messages_sent_total', 'Total WebSocket messages sent')
websocket_messages_received = Counter('websocket_messages_received_total', 'Total WebSocket messages received')
websocket_connection_duration = Histogram('websocket_connection_duration_seconds', 'WebSocket connection duration')

class MonitoredWebSocketManager(WebSocketManager):
    def __init__(self):
        super().__init__()
        self.connection_start_times = {}

    async def connect(self, websocket: WebSocket, token: str):
        connection_id = await super().connect(websocket, token)
        if connection_id:
            websocket_connections.inc()
            self.connection_start_times[connection_id] = time.time()
        return connection_id

    async def disconnect(self, connection_id: str):
        await super().disconnect(connection_id)
        websocket_connections.dec()

        # 记录连接持续时间
        if connection_id in self.connection_start_times:
            duration = time.time() - self.connection_start_times[connection_id]
            websocket_connection_duration.observe(duration)
            del self.connection_start_times[connection_id]

    async def send_message(self, connection_id: str, message: str):
        try:
            if connection_id in self.active_connections:
                await self.active_connections[connection_id].send_text(message)
                websocket_messages_sent.inc()
        except Exception as e:
            logger.error(f"Failed to send WebSocket message: {e}")

    async def receive_message(self, connection_id: str, message: str):
        websocket_messages_received.inc()
        # 处理接收到的消息
```

#### Webhook监控指标

```go
package notification

import (
    "github.com/prometheus/client_golang/prometheus"
    "github.com/prometheus/client_golang/prometheus/promauto"
)

var (
    webhookRequestsTotal = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "webhook_requests_total",
            Help: "Total number of webhook requests",
        },
        []string{"url", "method", "status"},
    )

    webhookDuration = promauto.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "webhook_duration_seconds",
            Help: "Webhook request duration",
            Buckets: prometheus.DefBuckets,
        },
        []string{"url", "method"},
    )

    webhookQueueSize = promauto.NewGaugeVec(
        prometheus.GaugeOpts{
            Name: "webhook_queue_size",
            Help: "Current webhook queue size",
        },
        []string{"module"},
    )
)

func SendWebhookWithMetrics(url, method, data, contentType string) error {
    start := time.Now()

    var err error
    var statusCode string

    if method == "GET" {
        _, err = utils.Requests.HttpGet(url)
    } else {
        err, _ = utils.Requests.HttpPost(url, []byte(data), contentType)
    }

    if err != nil {
        statusCode = "error"
    } else {
        statusCode = "success"
    }

    // 记录指标
    webhookRequestsTotal.WithLabelValues(url, method, statusCode).Inc()
    webhookDuration.WithLabelValues(url, method).Observe(time.Since(start).Seconds())

    return err
}

func UpdateQueueMetrics() {
    for module, queue := range NotificationQueues {
        webhookQueueSize.WithLabelValues(module).Set(float64(len(queue.Queue)))
    }
}
```

### 故障排除指南

#### 常见问题诊断

**1. WebSocket连接失败**

```bash
# 检查WebSocket端点
curl -i -N -H "Connection: Upgrade" \
     -H "Upgrade: websocket" \
     -H "Sec-WebSocket-Version: 13" \
     -H "Sec-WebSocket-Key: SGVsbG8sIHdvcmxkIQ==" \
     http://localhost:8082/

# 检查Nginx配置
nginx -t
systemctl status nginx

# 检查防火墙
iptables -L | grep 8082
ufw status
```

**2. Redis连接问题**

```bash
# 测试Redis连接
redis-cli -h redis-host -p 6379 -a password ping

# 检查Redis日志
tail -f /var/log/redis/redis-server.log

# 监控Redis性能
redis-cli -h redis-host -p 6379 -a password --latency
redis-cli -h redis-host -p 6379 -a password info memory
```

**3. Webhook发送失败**

```python
# 调试Webhook发送
import requests
import json

def test_webhook(url, data):
    try:
        response = requests.post(
            url,
            json=data,
            timeout=10,
            headers={'Content-Type': 'application/json'}
        )
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        return response.status_code == 200
    except requests.exceptions.Timeout:
        print("请求超时")
        return False
    except requests.exceptions.ConnectionError:
        print("连接错误")
        return False
    except Exception as e:
        print(f"其他错误: {e}")
        return False

# 测试钉钉Webhook
dingtalk_url = "https://oapi.dingtalk.com/robot/send?access_token=xxx"
test_data = {
    "msgtype": "text",
    "text": {
        "content": "测试消息"
    }
}

test_webhook(dingtalk_url, test_data)
```

**4. 消息队列积压**

```bash
# 检查Redis队列长度
redis-cli -h redis-host -p 6379 -a password eval "
local keys = redis.call('keys', 'NodeTask:*')
for i=1,#keys do
    local len = redis.call('llen', keys[i])
    if len > 0 then
        print(keys[i] .. ': ' .. len)
    end
end
" 0

# 检查扫描节点状态
redis-cli -h redis-host -p 6379 -a password hgetall node:scannode-001
```

#### 日志分析工具

```python
import re
from datetime import datetime
from collections import defaultdict

class LogAnalyzer:
    def __init__(self, log_file):
        self.log_file = log_file
        self.patterns = {
            'websocket_connect': r'WebSocket连接已建立.*user_id:(\w+)',
            'websocket_disconnect': r'WebSocket连接已关闭.*user_id:(\w+)',
            'webhook_success': r'SendNotification.*success.*url:(\S+)',
            'webhook_error': r'SendNotification.*error.*url:(\S+).*Error: (.+)',
            'redis_error': r'Redis.*error: (.+)',
            'task_start': r'(\w+) module start scanning.*target: (\S+)',
            'task_end': r'(\w+) module end scanning.*target: (\S+)',
        }

    def analyze(self):
        stats = defaultdict(int)
        errors = []

        with open(self.log_file, 'r') as f:
            for line_num, line in enumerate(f, 1):
                for pattern_name, pattern in self.patterns.items():
                    match = re.search(pattern, line)
                    if match:
                        stats[pattern_name] += 1

                        if 'error' in pattern_name:
                            errors.append({
                                'line': line_num,
                                'type': pattern_name,
                                'content': line.strip(),
                                'details': match.groups()
                            })

        return stats, errors

    def generate_report(self):
        stats, errors = self.analyze()

        print("=== 日志分析报告 ===")
        print(f"分析时间: {datetime.now()}")
        print(f"日志文件: {self.log_file}")
        print()

        print("统计信息:")
        for key, count in stats.items():
            print(f"  {key}: {count}")
        print()

        if errors:
            print("错误详情:")
            for error in errors[-10:]:  # 显示最近10个错误
                print(f"  行 {error['line']}: {error['type']}")
                print(f"    {error['content']}")
                print()

# 使用示例
analyzer = LogAnalyzer('/var/log/scopesentry/app.log')
analyzer.generate_report()
```

#### 性能调优建议

**1. Redis优化**

```bash
# 内存优化
redis-cli config set maxmemory-policy allkeys-lru
redis-cli config set maxmemory 2gb

# 持久化优化
redis-cli config set save "900 1 300 10 60 10000"

# 网络优化
redis-cli config set tcp-keepalive 300
redis-cli config set timeout 0
```

**2. WebSocket优化**

```python
# 连接池优化
WEBSOCKET_CONFIG = {
    'max_connections': 100,
    'max_message_size': 1024 * 1024,  # 1MB
    'ping_interval': 20,
    'ping_timeout': 10,
    'close_timeout': 10,
    'max_queue_size': 1000,
}

# 消息批量处理
class BatchWebSocketManager:
    def __init__(self):
        self.message_buffer = defaultdict(list)
        self.batch_size = 10
        self.flush_interval = 1.0

    async def start_batch_processor(self):
        while True:
            await asyncio.sleep(self.flush_interval)
            await self.flush_all_buffers()

    async def flush_all_buffers(self):
        for connection_id, messages in self.message_buffer.items():
            if messages:
                batch_message = '\n'.join(messages)
                await self.send_message(connection_id, batch_message)
                messages.clear()
```

**3. Webhook优化**

```go
// 连接池优化
var httpClient = &http.Client{
    Timeout: 10 * time.Second,
    Transport: &http.Transport{
        MaxIdleConns:        100,
        MaxIdleConnsPerHost: 10,
        IdleConnTimeout:     90 * time.Second,
        DisableKeepAlives:   false,
    },
}

// 批量发送优化
type WebhookBatcher struct {
    messages []string
    mutex    sync.Mutex
    ticker   *time.Ticker
}

func (wb *WebhookBatcher) AddMessage(message string) {
    wb.mutex.Lock()
    defer wb.mutex.Unlock()

    wb.messages = append(wb.messages, message)

    if len(wb.messages) >= batchSize {
        go wb.flush()
    }
}

func (wb *WebhookBatcher) flush() {
    wb.mutex.Lock()
    messages := make([]string, len(wb.messages))
    copy(messages, wb.messages)
    wb.messages = wb.messages[:0]
    wb.mutex.Unlock()

    if len(messages) > 0 {
        batchMessage := strings.Join(messages, "\n")
        sendWebhookBatch(batchMessage)
    }
}
```

---

## 📖 集成示例

### 钉钉机器人集成

```python
import requests
import json
import hmac
import hashlib
import base64
import urllib.parse
import time

class DingTalkBot:
    def __init__(self, webhook_url, secret=None):
        self.webhook_url = webhook_url
        self.secret = secret

    def _generate_sign(self, timestamp):
        """生成钉钉机器人签名"""
        if not self.secret:
            return None

        string_to_sign = f'{timestamp}\n{self.secret}'
        hmac_code = hmac.new(
            self.secret.encode('utf-8'),
            string_to_sign.encode('utf-8'),
            digestmod=hashlib.sha256
        ).digest()
        sign = urllib.parse.quote_plus(base64.b64encode(hmac_code))
        return sign

    def send_text(self, content, at_mobiles=None, is_at_all=False):
        """发送文本消息"""
        timestamp = str(round(time.time() * 1000))
        sign = self._generate_sign(timestamp)

        url = self.webhook_url
        if sign:
            url += f'&timestamp={timestamp}&sign={sign}'

        data = {
            "msgtype": "text",
            "text": {
                "content": content
            },
            "at": {
                "atMobiles": at_mobiles or [],
                "isAtAll": is_at_all
            }
        }

        response = requests.post(url, json=data, timeout=10)
        return response.json()

    def send_markdown(self, title, text):
        """发送Markdown消息"""
        timestamp = str(round(time.time() * 1000))
        sign = self._generate_sign(timestamp)

        url = self.webhook_url
        if sign:
            url += f'&timestamp={timestamp}&sign={sign}'

        data = {
            "msgtype": "markdown",
            "markdown": {
                "title": title,
                "text": text
            }
        }

        response = requests.post(url, json=data, timeout=10)
        return response.json()

# ScopeSentry消息格式化
class ScopeSentryFormatter:
    @staticmethod
    def format_vulnerability_alert(node_name, vulnerabilities):
        """格式化漏洞告警"""
        if not vulnerabilities:
            return None

        title = f"🚨 ScopeSentry漏洞告警 - {node_name}"

        content = f"## {title}\n\n"
        content += f"**检测时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        content += f"**扫描节点**: {node_name}\n"
        content += f"**漏洞数量**: {len(vulnerabilities)}\n\n"

        for vuln in vulnerabilities[:5]:  # 最多显示5个漏洞
            level_emoji = {
                'CRITICAL': '🔴',
                'HIGH': '🟠',
                'MEDIUM': '🟡',
                'LOW': '🟢'
            }.get(vuln.get('level', 'UNKNOWN'), '⚪')

            content += f"### {level_emoji} {vuln.get('level', 'UNKNOWN')} - {vuln.get('name', 'Unknown')}\n"
            content += f"**URL**: {vuln.get('url', 'N/A')}\n"
            content += f"**证据**: `{vuln.get('evidence', 'N/A')}`\n\n"

        if len(vulnerabilities) > 5:
            content += f"... 还有 {len(vulnerabilities) - 5} 个漏洞\n\n"

        content += "请及时处理相关安全问题！"

        return title, content

    @staticmethod
    def format_subdomain_discovery(node_name, subdomains):
        """格式化子域名发现"""
        if not subdomains:
            return None

        title = f"🔍 ScopeSentry子域名发现 - {node_name}"

        content = f"## {title}\n\n"
        content += f"**发现时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        content += f"**扫描节点**: {node_name}\n"
        content += f"**新增子域名**: {len(subdomains)}\n\n"

        for subdomain in subdomains[:10]:  # 最多显示10个
            content += f"- `{subdomain.get('subdomain')}` → {subdomain.get('ip')}\n"

        if len(subdomains) > 10:
            content += f"- ... 还有 {len(subdomains) - 10} 个子域名\n"

        return title, content

# 使用示例
dingtalk_bot = DingTalkBot(
    webhook_url="https://oapi.dingtalk.com/robot/send?access_token=xxx",
    secret="your_secret_here"
)

# 发送漏洞告警
vulnerabilities = [
    {
        'level': 'HIGH',
        'name': 'SQL Injection',
        'url': 'https://example.com/login',
        'evidence': 'union select 1,2,3'
    }
]

title, content = ScopeSentryFormatter.format_vulnerability_alert("scannode-001", vulnerabilities)
dingtalk_bot.send_markdown(title, content)
```

### 企业微信机器人集成

```python
class WeChatWorkBot:
    def __init__(self, webhook_url):
        self.webhook_url = webhook_url

    def send_text(self, content, mentioned_list=None, mentioned_mobile_list=None):
        """发送文本消息"""
        data = {
            "msgtype": "text",
            "text": {
                "content": content,
                "mentioned_list": mentioned_list or [],
                "mentioned_mobile_list": mentioned_mobile_list or []
            }
        }

        response = requests.post(self.webhook_url, json=data, timeout=10)
        return response.json()

    def send_markdown(self, content):
        """发送Markdown消息"""
        data = {
            "msgtype": "markdown",
            "markdown": {
                "content": content
            }
        }

        response = requests.post(self.webhook_url, json=data, timeout=10)
        return response.json()

# 使用示例
wechat_bot = WeChatWorkBot("https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxx")

# 发送告警消息
alert_content = """
## 🚨 ScopeSentry安全告警

**检测时间**: 2025-01-08 14:30:00
**扫描节点**: scannode-001
**告警类型**: 高危漏洞发现

### 漏洞详情
- **URL**: https://example.com/admin
- **类型**: SQL注入
- **等级**: <font color="warning">HIGH</font>
- **证据**: union select 1,2,3

请相关人员及时处理！
"""

wechat_bot.send_markdown(alert_content)
```

### Slack集成

```python
import json
import requests

class SlackBot:
    def __init__(self, webhook_url):
        self.webhook_url = webhook_url

    def send_message(self, text, channel=None, username=None, icon_emoji=None, attachments=None):
        """发送Slack消息"""
        data = {
            "text": text,
            "channel": channel,
            "username": username or "ScopeSentry",
            "icon_emoji": icon_emoji or ":shield:",
            "attachments": attachments or []
        }

        # 移除None值
        data = {k: v for k, v in data.items() if v is not None}

        response = requests.post(self.webhook_url, json=data, timeout=10)
        return response.json()

    def send_vulnerability_alert(self, node_name, vulnerabilities):
        """发送漏洞告警"""
        color_map = {
            'CRITICAL': 'danger',
            'HIGH': 'warning',
            'MEDIUM': 'good',
            'LOW': '#36a64f'
        }

        attachments = []
        for vuln in vulnerabilities[:5]:
            attachment = {
                "color": color_map.get(vuln.get('level'), 'good'),
                "title": f"{vuln.get('level')} - {vuln.get('name')}",
                "title_link": vuln.get('url'),
                "fields": [
                    {
                        "title": "URL",
                        "value": vuln.get('url', 'N/A'),
                        "short": True
                    },
                    {
                        "title": "Evidence",
                        "value": f"`{vuln.get('evidence', 'N/A')}`",
                        "short": True
                    }
                ],
                "footer": f"ScopeSentry - {node_name}",
                "ts": int(time.time())
            }
            attachments.append(attachment)

        text = f":warning: *ScopeSentry Security Alert* - {len(vulnerabilities)} vulnerabilities found on {node_name}"

        return self.send_message(text, attachments=attachments)

# 使用示例
slack_bot = SlackBot("https://hooks.slack.com/services/xxx/xxx/xxx")

vulnerabilities = [
    {
        'level': 'HIGH',
        'name': 'SQL Injection',
        'url': 'https://example.com/login',
        'evidence': 'union select 1,2,3'
    }
]

slack_bot.send_vulnerability_alert("scannode-001", vulnerabilities)
```

---

## 📋 总结与展望

### 当前实现总结

ScopeSentry的消息推送与Webhook机制实现了一个相对完整的分布式通知系统，具有以下特点：

#### ✅ 优势特性
1. **多层次通信架构**：Redis发布/订阅 + WebSocket + Webhook的组合
2. **实时性能优秀**：WebSocket提供毫秒级的日志推送
3. **批量处理机制**：有效减少网络开销和系统负载
4. **模块化设计**：支持不同类型的通知独立配置
5. **多平台支持**：支持钉钉、企微、Slack等主流平台

#### ⚠️ 存在问题
1. **安全性不足**：WebSocket无认证、消息无加密
2. **可靠性欠缺**：缺乏重试机制和死信队列
3. **资源管理**：内存泄漏风险和连接数无限制
4. **监控缺失**：缺乏完善的指标监控和告警

### 改进路线图

#### 短期改进 (1-2个月)
- [ ] 实现WebSocket JWT认证机制
- [ ] 添加Webhook重试和死信队列
- [ ] 优化内存管理，防止LOG_INFO泄漏
- [ ] 添加基础监控指标收集

#### 中期改进 (3-6个月)
- [ ] 实现消息加密和签名验证
- [ ] 添加基于角色的权限控制
- [ ] 实现消息优先级队列
- [ ] 完善监控和告警系统

#### 长期规划 (6-12个月)
- [ ] 支持消息持久化和回放
- [ ] 实现分布式消息队列
- [ ] 添加消息路由和过滤规则
- [ ] 支持自定义Webhook模板

### 最佳实践建议

#### 1. 部署建议
- 使用Redis集群保证高可用性
- 配置Nginx负载均衡和SSL终止
- 设置合理的资源限制和超时时间
- 实施完善的监控和日志收集

#### 2. 安全建议
- 启用WebSocket认证和授权
- 使用HTTPS/WSS加密传输
- 验证Webhook URL的合法性
- 实施API访问频率限制

#### 3. 性能建议
- 合理配置批量处理参数
- 使用连接池复用网络连接
- 实施消息压缩减少带宽
- 定期清理过期数据

#### 4. 运维建议
- 建立完善的监控体系
- 制定故障处理流程
- 定期备份配置数据
- 实施灰度发布策略

### 技术演进方向

#### 1. 云原生化
- 容器化部署和编排
- 服务网格集成
- 自动扩缩容支持
- 多云部署能力

#### 2. 智能化
- 基于AI的异常检测
- 智能消息过滤和聚合
- 自适应通知策略
- 预测性告警

#### 3. 标准化
- 遵循OpenTelemetry标准
- 支持CloudEvents规范
- 实现Webhook标准化
- 集成主流监控平台

---

## 📚 参考资料

### 官方文档
- [FastAPI WebSocket文档](https://fastapi.tiangolo.com/advanced/websockets/)
- [Redis发布订阅文档](https://redis.io/docs/manual/pubsub/)
- [Go Channel文档](https://golang.org/doc/effective_go#channels)

### 相关标准
- [RFC 6455 - WebSocket协议](https://tools.ietf.org/html/rfc6455)
- [RFC 7692 - WebSocket压缩扩展](https://tools.ietf.org/html/rfc7692)
- [CloudEvents规范](https://cloudevents.io/)
- [OpenTelemetry规范](https://opentelemetry.io/)

### 第三方集成
- [钉钉机器人开发文档](https://developers.dingtalk.com/document/app/custom-robot-access)
- [企业微信机器人文档](https://developer.work.weixin.qq.com/document/path/91770)
- [Slack Webhook文档](https://api.slack.com/messaging/webhooks)

### 开源项目参考
- [Prometheus监控](https://prometheus.io/)
- [Grafana可视化](https://grafana.com/)
- [Jaeger分布式追踪](https://www.jaegertracing.io/)
- [ELK日志分析](https://www.elastic.co/elk-stack)

### 性能测试工具
- [WebSocket King](https://websocketking.com/) - WebSocket连接测试
- [Artillery](https://artillery.io/) - 负载测试工具
- [Redis-benchmark](https://redis.io/topics/benchmarks) - Redis性能测试

---

## 🤝 贡献指南

### 问题反馈
如果您在使用过程中遇到问题或有改进建议，请通过以下方式反馈：

1. **GitHub Issues**：提交详细的问题描述和复现步骤
2. **技术讨论**：参与社区技术讨论和经验分享
3. **文档改进**：提交文档修正和补充建议

### 开发贡献
欢迎开发者参与ScopeSentry的改进：

1. **代码贡献**：提交Pull Request改进现有功能
2. **功能开发**：开发新的通知渠道和集成
3. **测试完善**：编写单元测试和集成测试
4. **文档维护**：更新和完善技术文档

### 联系方式
- **项目地址**：[GitHub Repository]
- **技术支持**：[Support Email]
- **社区讨论**：[Discord/Telegram]

---

**文档版本：** v1.0
**最后更新：** 2025-01-08
**适用版本：** ScopeSentry v1.7+
**维护团队：** ScopeSentry开发团队
**文档许可：** MIT License
