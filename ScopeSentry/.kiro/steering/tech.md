# Technology Stack

## Backend Stack
- **Framework**: FastAPI (Python 3.7+)
- **Database**: MongoDB with Motor async driver
- **Cache/Queue**: Redis
- **Task Scheduling**: APScheduler with MongoDB jobstore
- **Authentication**: JWT with PyJWT
- **Logging**: Loguru
- **HTTP Client**: httpx for async requests
- **WebSocket**: Native FastAPI WebSocket support

## Frontend Stack
- **Framework**: Vue.js 3 with TypeScript
- **UI Library**: Element Plus
- **Build Tool**: Vite
- **Admin Template**: vue-element-plus-admin

## Infrastructure
- **Containerization**: Docker
- **Web Server**: Uvicorn ASGI server
- **Reverse Proxy**: Nginx (in production)
- **Time Zone**: Asia/Shanghai (configurable)

## Key Dependencies
```
fastapi==0.103.2
motor==3.3.2          # MongoDB async driver
redis==5.0.3
APScheduler==3.10.4
loguru==0.7.2
PyJWT==2.8.0
uvicorn==0.22.0
pydantic==2.5.3
```

## Configuration
- **Config File**: `config.yaml` (auto-generated if missing)
- **Environment Variables**: Support for Docker deployment
- **Plugin System**: Custom plugin key in `PLUGINKEY` file

## Common Commands

### Development
```bash
# Install dependencies
pip install -r requirements.txt

# Run development server
python main.py

# Run with auto-reload (development)
uvicorn main:app --host 0.0.0.0 --port 8082 --reload
```

### Docker Deployment
```bash
# Build container
docker build -t scopesentry .

# Run container
docker run -p 8082:8082 scopesentry

# Use docker-compose
docker-compose -f single-host-deployment.yml up -d
```

### Database Operations
- MongoDB connection configured via `config.yaml` or environment variables
- Automatic database initialization on startup
- Version-based migration system in `core/update.py`

### API Documentation
- Swagger UI: `http://localhost:8082/api/docs`
- ReDoc: `http://localhost:8082/api/redoc`