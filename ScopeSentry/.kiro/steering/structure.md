# Project Structure

## Root Directory Layout
```
ScopeSentry/
├── main.py                 # FastAPI application entry point
├── requirements.txt        # Python dependencies
├── dockerfile             # Docker container configuration
├── config.yaml            # Runtime configuration (auto-generated)
├── PLUGINKEY              # Plugin authentication key
├── version.json           # Version tracking for updates
└── start.sh               # Container startup script
```

## Core Architecture (`core/`)
```
core/
├── __init__.py
├── config.py              # Configuration management and globals
├── db.py                  # MongoDB connection and database setup
├── redis_handler.py       # Redis operations and pub/sub
├── apscheduler_handler.py # Task scheduling configuration
├── update.py              # Database migration system
├── util.py                # Common utilities
├── default.py             # Default configurations
└── handler/               # Core business logic handlers
    └── task.py
```

## API Layer (`api/`)
```
api/
├── __init__.py
├── users.py               # Authentication and user management
├── configuration.py       # System configuration endpoints
├── fingerprint.py         # Service fingerprinting
├── poc.py                 # Proof of Concept management
├── notification.py        # Alert and notification system
├── system.py              # System information and health
├── export.py              # Data export functionality
├── project_aggregation.py # Project data aggregation
├── asset/                 # Asset management modules
│   ├── app.py            # Application asset handling
│   ├── asset.py          # Core asset operations
│   ├── crawler.py        # Web crawling functionality
│   ├── dirscan.py        # Directory scanning
│   ├── page_monitoring.py # Page change monitoring
│   ├── root_domain.py    # Root domain management
│   ├── subdomain.py      # Subdomain enumeration
│   ├── url.py            # URL management
│   ├── vulnerability.py  # Vulnerability scanning
│   └── sensitive.py      # Sensitive data detection
├── dictionary/           # Dictionary and wordlist management
├── node/                 # Distributed node management
├── plugins/              # Plugin system
├── project/              # Project management
└── task/                 # Task scheduling and execution
```

## Data Storage (`data/`)
```
data/
├── mongodb/              # MongoDB data files
└── redis/                # Redis persistence data
```

## Static Assets (`static/`)
```
static/
├── index.html            # Frontend entry point
├── favicon.ico
├── logo.png
└── assets/               # Frontend build artifacts (JS, CSS, images)
```

## Configuration Files (`dicts/`)
```
dicts/
├── ScopeSentry.FingerprintRules.json  # Service fingerprint rules
├── ScopeSentry.PocList.json           # POC definitions
├── ScopeSentry.SensitiveRule.json     # Sensitive data patterns
├── ScopeSentry.project.json           # Project templates
├── dirDict                            # Directory wordlists
├── domainDict                         # Domain wordlists
└── fingerprint                        # Fingerprint databases
```

## Development Guidelines

### Module Organization
- **API modules**: Follow RESTful patterns with FastAPI routers
- **Core modules**: Shared utilities and database operations
- **Asset modules**: Domain-specific scanning and analysis logic

### File Naming Conventions
- Python files: `snake_case.py`
- Configuration files: `kebab-case.json/yaml`
- Static assets: Follow frontend build conventions

### Import Structure
- Core imports from `core/` package
- API imports from `api/` package
- Cross-module imports should be minimal and well-defined

### Database Collections
- Collections follow the module structure (assets, projects, tasks, etc.)
- MongoDB collections are created dynamically based on usage
- Version-controlled schema migrations in `core/update.py`