# Product Overview

**ScopeSentry** is a distributed cybersecurity reconnaissance and vulnerability scanning platform designed for asset mapping and security assessment.

## Core Purpose
- Asset discovery and mapping across distributed networks
- Comprehensive vulnerability scanning and security assessment
- Multi-node distributed scanning architecture
- Real-time monitoring and alerting for security threats

## Key Features
- **Plugin System**: Extensible architecture supporting custom security tools
- **Asset Discovery**: Subdomain enumeration, port scanning, service identification
- **Vulnerability Assessment**: Automated vulnerability scanning with POC support
- **Information Gathering**: Directory scanning, sensitive data detection, URL extraction
- **Monitoring**: Page monitoring, asset change detection, webhook notifications
- **Multi-Node Support**: Distributed scanning across multiple nodes for scalability

## Target Users
Security professionals, penetration testers, and organizations requiring comprehensive asset visibility and vulnerability management across their infrastructure.

## Architecture
- **Server**: Python FastAPI backend with MongoDB and Redis
- **Scanner**: Go-based scanning engine (separate repository)
- **Frontend**: Vue.js with Element Plus UI framework
- **Deployment**: Docker containerized with multi-node support