Metadata-Version: 2.4
Name: Cython
Version: 3.1.2
Summary: The Cython compiler for writing C extensions in the Python language.
Home-page: https://cython.org/
Author: <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al.
Author-email: <EMAIL>
License: Apache-2.0
Project-URL: Documentation, https://cython.readthedocs.io/
Project-URL: Donate, https://cython.readthedocs.io/en/latest/src/donating.html
Project-URL: Source Code, https://github.com/cython/cython
Project-URL: Bug Tracker, https://github.com/cython/cython/issues/
Project-URL: User Group, https://groups.google.com/g/cython-users
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Programming Language :: Python :: Implementation :: Stackless
Classifier: Programming Language :: C
Classifier: Programming Language :: C++
Classifier: Programming Language :: Cython
Classifier: Topic :: Software Development :: Code Generators
Classifier: Topic :: Software Development :: Compilers
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Typing :: Typed
Requires-Python: >=3.8
Description-Content-Type: text/x-rst
License-File: LICENSE.txt
License-File: COPYING.txt
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: license
Dynamic: license-file
Dynamic: project-url
Dynamic: requires-python
Dynamic: summary

The Cython language makes writing C extensions for the Python language as
easy as Python itself.  Cython is a source code translator based on Pyrex_,
but supports more cutting edge functionality and optimizations.

The Cython language is a superset of the Python language (almost all Python
code is also valid Cython code), but Cython additionally supports optional
static typing to natively call C functions, operate with C++ classes and
declare fast C types on variables and class attributes.  This allows the
compiler to generate very efficient C code from Cython code.

This makes Cython the ideal language for writing glue code for external
C/C++ libraries, and for fast C modules that speed up the execution of
Python code.

The newest Cython release can always be downloaded from https://cython.org/.
Unpack the tarball or zip file, enter the directory, and then run::

    pip install .

Note that for one-time builds, e.g. for CI/testing, on platforms that are not
covered by one of the wheel packages provided on PyPI *and* the pure Python wheel
that we provide is not used, it is substantially faster than a full source build
to install an uncompiled (slower) version of Cython with::

    NO_CYTHON_COMPILE=true pip install .

.. _Pyrex: https://www.cosc.canterbury.ac.nz/greg.ewing/python/Pyrex/

3.1.2 (2025-06-09)
==================

Bugs fixed
----------

* Attribute lookups failed on the ``bool`` builtin type.
  (Github issue https://github.com/cython/cython/issues/6905)

* Type checks on or-ed union types could incorrectly return false.
  (Github issue https://github.com/cython/cython/issues/6420)

* Negative list indexing could accidentally wrap around twice in PyPy and the Limited API.

* Iterating over literal sequences with starred (unpacked) items could infer a wrong
  type for the loop variable and fail to assign the values.
  (Github issue https://github.com/cython/cython/issues/6924)

* Calls to C functions taking exception types failed to check for a `None` argument.
  (Github issue https://github.com/cython/cython/issues/6420)

* Fused functions had an incorrect ``__module__`` attribute.
  (Github issue https://github.com/cython/cython/issues/6897)

* The type of Cython implemented functions had an incorrect ``__module__`` attribute.
  (Github issue https://github.com/cython/cython/issues/6841)

* Errors while indexing into ``bytearray`` or ``str`` in ``nogil`` sections could crash.
  (Github issue https://github.com/cython/cython/issues/6947)

* ``bytearray.append()`` could silently accept some invalid character numbers.

* The C++11 ``<type_traits>`` header was included regardless of the C++ version.
  (Github issue https://github.com/cython/cython/issues/6896)

* ``PyDict_GetItemStringRef()`` was accidentally used in older Limited API versions.
  (Github issue https://github.com/cython/cython/issues/6914)

* ``abort()`` was used but not always available in the Limited API.
  (Github issue https://github.com/cython/cython/issues/6918)

* Some dependencies were missing from the ``depfile``.
  (Github issue https://github.com/cython/cython/issues/6938)

* Embedded function signatures were not always separated from the existing docstring.
  (Github issue https://github.com/cython/cython/issues/6904)

* ``numpy.math`` was missing from ``Cython/Includes/`` and could not be cimported.
  (Github issue https://github.com/cython/cython/issues/6859)

* Some tests were adapted for NumPy 2.x.
  (Github issue https://github.com/cython/cython/issues/6898)

* Some C compiler warnings were fixed.
  (Github issue https://github.com/cython/cython/issues/6870)

* ``Cython.Build`` was not officially exposing the ``cythonize`` function.
  (Github issue https://github.com/cython/cython/issues/6934)
