from Cython.TestUtils import CythonTest
import Cython.Compiler.Errors as Errors
from Cython.Compiler.Nodes import *
from Cython.Compiler.ParseTreeTransforms import *
from Cython.Compiler.Buffer import *


class TestBufferParsing(CythonTest):
    # First, we only test the raw parser, i.e.
    # the number and contents of arguments are NOT checked.
    # However "dtype"/the first positional argument is special-cased
    #  to parse a type argument rather than an expression

    def parse(self, s):
        return self.should_not_fail(lambda: self.fragment(s)).root

    def not_parseable(self, expected_error, s):
        e = self.should_fail(lambda: self.fragment(s),  Errors.CompileError)
        self.assertEqual(expected_error, e.message_only)

    def test_basic(self):
        t = self.parse("cdef object[float, 4, ndim=2, foo=foo] x")
        bufnode = t.stats[0].base_type
        self.assertTrue(isinstance(bufnode, TemplatedTypeNode))
        self.assertEqual(2, len(bufnode.positional_args))
#        print bufnode.dump()
        # should put more here...

    def test_type_pos(self):
        self.parse("cdef object[short unsigned int, 3] x")

    def test_type_keyword(self):
        self.parse("cdef object[foo=foo, dtype=short unsigned int] x")

    def test_pos_after_key(self):
        self.not_parseable("Non-keyword arg following keyword arg",
                           "cdef object[foo=1, 2] x")


# See also tests/error/e_bufaccess.pyx and tets/run/bufaccess.pyx
# THESE TESTS ARE NOW DISABLED, the code they test was pretty much
# refactored away
class TestBufferOptions(CythonTest):
    # Tests the full parsing of the options within the brackets

    def nonfatal_error(self, error):
        # We're passing self as context to transform to trap this
        self.error = error
        self.assertTrue(self.expect_error)

    def parse_opts(self, opts, expect_error=False):
        assert opts != ""
        s = "def f():\n  cdef object[%s] x" % opts
        self.expect_error = expect_error
        root = self.fragment(s, pipeline=[NormalizeTree(self), PostParse(self)]).root
        if not expect_error:
            vardef = root.stats[0].body.stats[0]
            assert isinstance(vardef, CVarDefNode)  # use normal assert as this is to validate the test code
            buftype = vardef.base_type
            self.assertTrue(isinstance(buftype, TemplatedTypeNode))
            self.assertTrue(isinstance(buftype.base_type_node, CSimpleBaseTypeNode))
            self.assertEqual("object", buftype.base_type_node.name)
            return buftype
        else:
            self.assertTrue(len(root.stats[0].body.stats) == 0)

    def non_parse(self, expected_err, opts):
        self.parse_opts(opts, expect_error=True)
#        e = self.should_fail(lambda: self.parse_opts(opts))
        self.assertEqual(expected_err, self.error.message_only)

    def __test_basic(self):
        buf = self.parse_opts("unsigned short int, 3")
        self.assertTrue(isinstance(buf.dtype_node, CSimpleBaseTypeNode))
        self.assertTrue(buf.dtype_node.signed == 0 and buf.dtype_node.longness == -1)
        self.assertEqual(3, buf.ndim)

    def __test_dict(self):
        buf = self.parse_opts("ndim=3, dtype=unsigned short int")
        self.assertTrue(isinstance(buf.dtype_node, CSimpleBaseTypeNode))
        self.assertTrue(buf.dtype_node.signed == 0 and buf.dtype_node.longness == -1)
        self.assertEqual(3, buf.ndim)

    def __test_ndim(self):
        self.parse_opts("int, 2")
        self.non_parse(ERR_BUF_NDIM, "int, 'a'")
        self.non_parse(ERR_BUF_NDIM, "int, -34")

    def __test_use_DEF(self):
        t = self.fragment("""
        DEF ndim = 3
        def f():
            cdef object[int, ndim] x
            cdef object[ndim=ndim, dtype=int] y
        """, pipeline=[NormalizeTree(self), PostParse(self)]).root
        stats = t.stats[0].body.stats
        self.assertTrue(stats[0].base_type.ndim == 3)
        self.assertTrue(stats[1].base_type.ndim == 3)

    # add exotic and impossible combinations as they come along...


if __name__ == '__main__':
    import unittest
    unittest.main()
